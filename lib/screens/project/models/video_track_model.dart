import 'dart:io';
import 'package:uuid/uuid.dart';

class VideoTrackModel {
  final String id;
  final File originalFile;
  final File processedFile;
  final int startTime;
  final int endTime;
  final int totalDuration;
  final bool hasOriginalAudio;
  // Video trim fields - similar to AudioTrackModel
  final double videoTrimStart;
  final double videoTrimEnd;
  final double originalDuration; // Store original duration before trim
  final DateTime lastModified; // Timestamp for tracking changes
  // Stretch functionality fields
  final bool isImageBased; // Flag to identify image-converted videos
  final double? customDuration; // Custom duration for stretched image videos

  VideoTrackModel({
    String? id,
    required this.originalFile,
    required this.processedFile,
    this.startTime = 0,
    this.endTime = 0,
    this.totalDuration = 0,
    this.hasOriginalAudio = false,
    this.videoTrimStart = 0.0,
    double? videoTrimEnd,
    double? originalDuration,
    DateTime? lastModified,
    this.isImageBased = false,
    this.customDuration,
  })  : id = id ?? const Uuid().v4(),
        videoTrimEnd = videoTrimEnd ?? totalDuration.toDouble(),
        originalDuration = originalDuration ?? totalDuration.toDouble(),
        lastModified = lastModified ?? DateTime.now();

  VideoTrackModel copyWith({
    String? id,
    File? originalFile,
    File? processedFile,
    int? startTime,
    int? endTime,
    int? totalDuration,
    bool? hasOriginalAudio,
    double? videoTrimStart,
    double? videoTrimEnd,
    double? originalDuration,
    DateTime? lastModified,
    bool? isImageBased,
    double? customDuration,
  }) {
    return VideoTrackModel(
      id: id ?? this.id,
      originalFile: originalFile ?? this.originalFile,
      processedFile: processedFile ?? this.processedFile,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      totalDuration: totalDuration ?? this.totalDuration,
      hasOriginalAudio: hasOriginalAudio ?? this.hasOriginalAudio,
      videoTrimStart: videoTrimStart ?? this.videoTrimStart,
      videoTrimEnd: videoTrimEnd ?? this.videoTrimEnd,
      originalDuration: originalDuration ?? this.originalDuration,
      lastModified: lastModified ??
          DateTime.now(), // Always update timestamp when copying
      isImageBased: isImageBased ?? this.isImageBased,
      customDuration: customDuration ?? this.customDuration,
    );
  }
}
