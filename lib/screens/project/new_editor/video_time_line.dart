import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_track.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_trimmer.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/stretch_duration_selector.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/enums/track_type.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:ai_video_creator_editor/components/track_options.dart';

class VideoTimeline extends StatefulWidget {
  final VideoPlayerController? controller;
  final ScrollController? videoScrollController;

  const VideoTimeline({
    super.key,
    required this.controller,
    required this.videoScrollController,
  });

  @override
  State<VideoTimeline> createState() => _VideoTimelineState();
}

class _VideoTimelineState extends State<VideoTimeline>
    with AutomaticKeepAliveClientMixin {
  @override
  void initState() {
    widget.controller?.removeListener(_onPlayScrollToCurrentVideoPosition);
    widget.controller?.addListener(() {
      if (widget.controller?.value.isPlaying ?? false) {
        _onPlayScrollToCurrentVideoPosition();
      }
    });
    widget.videoScrollController
        ?.removeListener(_onScrollToUpdateVideoPosition);
    widget.videoScrollController?.addListener(() {
      if (widget.videoScrollController?.position.isScrollingNotifier.value ??
          false) {
        _onScrollToUpdateVideoPosition();
      }
    });
    super.initState();
  }

  Future<void> _onScrollToUpdateVideoPosition() async {
    if (widget.videoScrollController?.hasClients != true ||
        widget.controller == null) return;

    final controller = widget.controller!;
    final wasPlayingBeforeScroll = controller.value.isPlaying;

    await controller.pause();

    final scrollController = widget.videoScrollController!;
    final double progress =
        scrollController.offset / scrollController.position.maxScrollExtent;

    Duration newPosition = controller.value.duration * progress;
    await controller.seekTo(newPosition);

    if (wasPlayingBeforeScroll) {
      await Future.delayed(Duration(milliseconds: 500));
      widget.controller?.play();
    }
  }

  void _onPlayScrollToCurrentVideoPosition() {
    if (widget.controller == null ||
        widget.videoScrollController?.hasClients != true) return;

    final controller = widget.controller!;
    if (controller.value.duration == Duration.zero) return;

    final scrollController = widget.videoScrollController!;
    final int position = controller.value.position.inMilliseconds;
    final int duration = controller.value.duration.inMilliseconds;

    double progress = position / duration;
    if (duration - position <= 500) progress = 1.0;

    final double targetOffset =
        scrollController.position.maxScrollExtent * progress.clamp(0.0, 1.0);
    scrollController.jumpTo(targetOffset);
  }

  @override
  void dispose() {
    widget.controller?.removeListener(_onPlayScrollToCurrentVideoPosition);
    widget.videoScrollController
        ?.removeListener(_onScrollToUpdateVideoPosition);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final width = MediaQuery.of(context).size.width;
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        // DEBUG LOGGING
        // print(
        //     'VideoTimeline: videoTracks count = \\${provider.videoTracks.length}');
        // for (var t in provider.videoTracks) {
        //   print(
        //       '  Track id: \\${t.id}, duration: \\${t.totalDuration}, file: \\${t.processedFile.path}');
        // }
        final timelineDuration = provider.videoDuration;
        final timelineWidth = timelineDuration * (width / 8);
        return Container(
          margin: EdgeInsets.only(right: width / 2),
          width: timelineWidth,
          child: Row(
            children: provider.videoTracks.asMap().entries.map((entry) {
              final index = entry.key;
              final videoTrack = entry.value;
              return GestureDetector(
                onTap: () async {
                  final selectedIndex = provider.selectedVideoTrackIndex;
                  if (selectedIndex == index) return;
                  provider.setVideoTrackIndex(index);
                },
                onLongPressStart: (details) async {
                  final tapOffset = details.globalPosition;
                  final provider = context.read<VideoEditorProvider>();
                  OverlayEntry? entry;
                  // Don't show mute option for image-based videos
                  final bool showMute =
                      videoTrack.hasOriginalAudio && !videoTrack.isImageBased;
                  final bool? isMuted =
                      showMute ? provider.isVideoMuted(videoTrack.id) : null;
                  entry = OverlayEntry(
                    builder: (context) => TrackOptions(
                      offset: tapOffset,
                      trackType: TrackType.video,
                      onTap: () {
                        entry?.remove();
                      },
                      onTrim: () {
                        entry?.remove();
                        _openVideoTrimmer(context, videoTrack, index);
                      },
                      onDelete: () async {
                        context.loaderOverlay.show();
                        try {
                          await provider.removeVideoTrack(index);
                        } finally {
                          context.loaderOverlay.hide();
                        }
                        entry?.remove();
                      },
                      onMute: showMute
                          ? () {
                              provider.toggleVideoMute(videoTrack.id);
                              entry?.remove();
                            }
                          : null,
                      onStretch: videoTrack.isImageBased
                          ? () {
                              entry?.remove();
                              _showStretchDuration(
                                  context, videoTrack, index, provider);
                            }
                          : null,
                      showStretch: videoTrack.isImageBased,
                      showMute: !videoTrack.isImageBased,
                      isMuted: isMuted,
                    ),
                  );
                  Overlay.of(context, rootOverlay: true).insert(entry);
                },
                child: VideoTrack(
                  key: ValueKey(
                      '${videoTrack.id}_${videoTrack.lastModified.millisecondsSinceEpoch}'),
                  videoTrack: videoTrack,
                  index: index,
                  isSelected: provider.selectedVideoTrackIndex == index,
                  selectedTrackBorderColor: provider.selectedTrackBorderColor,
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  void _openVideoTrimmer(
      BuildContext context, VideoTrackModel videoTrack, int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoTrimmer(
          videoTrack: videoTrack,
          trackIndex: index,
        ),
      ),
    );
  }

  void _showStretchDuration(BuildContext context, VideoTrackModel videoTrack,
      int index, VideoEditorProvider provider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StretchDurationSelector(
        currentDuration: videoTrack.totalDuration,
        onDurationSelected: (newDuration) async {
          await provider.stretchImageVideo(index, newDuration);
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
