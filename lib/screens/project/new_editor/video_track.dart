import 'dart:async';
import 'dart:io';

import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

class VideoTrack extends StatefulWidget {
  const VideoTrack({
    super.key,
    required this.videoTrack,
    required this.index,
    required this.isSelected,
    required this.selectedTrackBorderColor,
  });

  final VideoTrackModel videoTrack;
  final int index;
  final bool isSelected;
  final Color selectedTrackBorderColor;

  @override
  State<VideoTrack> createState() => _VideoTrackState();
}

class _VideoTrackState extends State<VideoTrack>
    with AutomaticKeepAliveClientMixin {
  final ValueNotifier<List<File>> _thumbnailNotifier =
      ValueNotifier<List<File>>([]);
  bool _isGeneratingThumbnails = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      _isGeneratingThumbnails = true;
    });
    _generateThumbnailAtTime(widget.videoTrack.processedFile.path);
  }

  @override
  void didUpdateWidget(VideoTrack oldWidget) {
    super.didUpdateWidget(oldWidget);

    print("=== VideoTrack didUpdateWidget called ===");
    print("Old file: ${oldWidget.videoTrack.processedFile.path}");
    print("New file: ${widget.videoTrack.processedFile.path}");
    print("Old trim start: ${oldWidget.videoTrack.videoTrimStart}");
    print("New trim start: ${widget.videoTrack.videoTrimStart}");
    print("Old trim end: ${oldWidget.videoTrack.videoTrimEnd}");
    print("New trim end: ${widget.videoTrack.videoTrimEnd}");

    // Regenerate thumbnails if the processed file has changed (e.g., after trimming)
    if (oldWidget.videoTrack.processedFile.path !=
            widget.videoTrack.processedFile.path ||
        oldWidget.videoTrack.videoTrimStart !=
            widget.videoTrack.videoTrimStart ||
        oldWidget.videoTrack.videoTrimEnd != widget.videoTrack.videoTrimEnd) {
      print("Track changed, regenerating thumbnails...");
      setState(() {
        _isGeneratingThumbnails = true;
      });
      _generateThumbnailAtTime(widget.videoTrack.processedFile.path);
    } else {
      print("No track changes detected, skipping thumbnail regeneration");
    }
  }

  Future<void> _generateThumbnailAtTime(String filePath) async {
    try {
      final Directory tempDir = await getTemporaryDirectory();

      print("=== Generating thumbnails for track ${widget.videoTrack.id} ===");
      print("File path: $filePath");
      print("Track trim start: ${widget.videoTrack.videoTrimStart}");
      print("Track trim end: ${widget.videoTrack.videoTrimEnd}");
      print("Track total duration: ${widget.videoTrack.totalDuration}");

      // Clear old thumbnails for this track first
      await _clearOldThumbnails(tempDir);

      final String outputPattern =
          '${tempDir.path}/video_track${widget.index}_${widget.videoTrack.id}_${widget.videoTrack.lastModified.millisecondsSinceEpoch}_frame_%d.jpg';

      // For trimmed videos, we want to show thumbnails from the trimmed content
      // The trimmed video file already contains only the desired segment
      final command =
          '-i "$filePath" -vf "fps=1,scale=160:90" -q:v 2 "$outputPattern"';

      print("Generating thumbnails with command: $command");

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      print("Thumbnail generation return code: $returnCode");

      if (!ReturnCode.isSuccess(returnCode)) {
        print("Failed to generate thumbnails for ${widget.videoTrack.id}");
        if (mounted) _thumbnailNotifier.value = [];
        return;
      }

      final List<File> files = (await tempDir.list().where((entity) {
        return entity.path.contains(
            "video_track${widget.index}_${widget.videoTrack.id}_${widget.videoTrack.lastModified.millisecondsSinceEpoch}");
      }).map((entity) {
        return File(entity.path);
      }).toList())
        ..sort((a, b) {
          int extractNumber(String path) {
            String fileName = p.basename(path);
            final index =
                int.tryParse(fileName.split("_").last.split(".").first) ?? 0;
            return index;
          }

          return extractNumber(a.path).compareTo(extractNumber(b.path));
        });

      print(
          "Generated ${files.length} thumbnails for track ${widget.videoTrack.id}");
      if (mounted) {
        _thumbnailNotifier.value = files;
        setState(() {
          _isGeneratingThumbnails = false;
        });
      }
    } catch (e) {
      print("Error generating thumbnails: $e");
      if (mounted) {
        _thumbnailNotifier.value = [];
        setState(() {
          _isGeneratingThumbnails = false;
        });
      }
    }
  }

  Future<void> _clearOldThumbnails(Directory tempDir) async {
    try {
      // Clear ALL old thumbnails for this track (any timestamp)
      final oldFiles = await tempDir.list().where((entity) {
        return entity.path.contains(
                "video_track${widget.index}_${widget.videoTrack.id}_") &&
            entity.path.contains("_frame_");
      }).toList();

      for (final file in oldFiles) {
        if (file is File) {
          await file.delete();
        }
      }
      print(
          "Cleared ${oldFiles.length} old thumbnails for track ${widget.videoTrack.id}");
    } catch (e) {
      print("Error clearing old thumbnails: $e");
    }
  }

  @override
  void dispose() {
    _thumbnailNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final width = MediaQuery.of(context).size.width;
    return ValueListenableBuilder<List<File>>(
      valueListenable: _thumbnailNotifier,
      builder: (context, thumbnails, _) {
        return Container(
          width: (width / 8) * widget.videoTrack.totalDuration,
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.selectedTrackBorderColor,
              width: widget.isSelected ? 2 : 0,
            ),
          ),
          child: Stack(
            children: [
              // Show loading indicator or thumbnails
              _isGeneratingThumbnails
                  ? Container(
                      height: 60, // Match thumbnail height
                      child: Align(
                        alignment: Alignment
                            .centerLeft, // Position at start of timeline
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize:
                                MainAxisSize.min, // Take minimum space needed
                            children: [
                              SizedBox(
                                width: 14,
                                height: 14,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white.withValues(alpha: 0.9),
                                  ),
                                ),
                              ),
                              SizedBox(width: 6),
                              Text(
                                'Loading...',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.9),
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      scrollDirection: Axis.horizontal,
                      itemCount: thumbnails.length,
                      itemBuilder: (context, index) {
                        return SizedBox(
                          width: width / 8,
                          child: thumbnails.isNotEmpty
                              ? Image.file(
                                  thumbnails[index],
                                  fit: BoxFit.cover,
                                )
                              : const Icon(Icons.broken_image_outlined),
                        );
                      },
                    ),
              // No icon overlay for no audio; only show in popup
            ],
          ),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
